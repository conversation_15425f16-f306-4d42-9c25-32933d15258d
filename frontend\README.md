# 📚 91写作 - AI智能小说创作工具

> 基于 Vue 3 + Element Plus 的专业AI小说创作平台，集成先进AI模型，提供完整的创作工具链

[![Vue](https://img.shields.io/badge/Vue-3.3.8-4FC08D?style=flat-square&logo=vue.js)](https://vuejs.org/)
[![Element Plus](https://img.shields.io/badge/Element%20Plus-2.4.2-409EFF?style=flat-square&logo=element)](https://element-plus.org/)
[![Vite](https://img.shields.io/badge/Vite-4.5.0-646CFF?style=flat-square&logo=vite)](https://vitejs.dev/)
[![License](https://img.shields.io/badge/License-MIT-green?style=flat-square)](LICENSE)

<img src=".\image\微信截图_20250618183833.png" />

## 🎉 在线演示

- 🌐 **演示地址**: https://xiezuo.91hub.vip
- 📱 **支持设备**: 浏览器、PC


### 🎬 视频教程
- [API配置教程](https://www.bilibili.com/video/BV1keKgzaER2)
- [本地部署教程](https://www.bilibili.com/video/BV1AYKgzAEne)

## ✨ 核心特色

### 🌈 **产品声明**
- 91写作为纯前端项目，所有数据均保存在本地，不提供云同步服务
- 本项目大模型API全部为用户自行配置，不提供公共API服务
- 本项目内置提示词均为预设演示，可以配置自己的提示词库来使用

### 🤖 **智能创作引擎**
- 支持集成主流AI模型（GPT、Claude、Gemini、DeepSeek等OpenAi格式API）
- 上下文感知的智能续写
- 多样化的小说生成算法
- 多模型切换，适应不同创作需求

### 🎨 **完整创作工具链**
- 从构思到成文的全流程支持
- 专业的富文本编辑环境
- 智能大纲生成与章节管理
- 实时写作统计与目标跟踪

### 🌍 **世界观构建系统**
- 复杂世界观模板化管理
- AI辅助世界设定生成
- 格式化模板确保一致性
- 科幻修仙等特殊题材专业支持

### 📊 **数据管理中心**
- 本地化数据存储
- 分类导入导出功能
- 云端同步（计划中）
- 完整的备份恢复机制

## 🚀 主要功能

### 📖 **小说管理**
- **项目创建**: 多类型小说模板，一键生成项目结构
- **元数据管理**: 标题、封面、简介、标签、状态管理
- **章节编辑**: 专业的写作编辑器，支持Markdown和富文本
- **智能章节选择**: 进入编辑模块自动选中第一章节
- **章节状态管理**: 草稿/完成/发表三状态系统，可视化管理
- **版本控制**: 自动保存，防止内容丢失
- **统计分析**: 字数统计、阅读时间估算、创作进度

### 🎯 **写作目标**
- **目标设定**: 每日/每周/每月字数目标
- **进度跟踪**: 实时进度监控，完成率可视化
- **连续记录**: 写作天数统计，培养创作习惯
- **成就系统**: 目标完成奖励，激励持续创作
- **数据同步**: 多页面实时数据同步

### 🎭 **动态类型管理**
- **预设类型**: 玄幻、都市、历史、科幻、武侠、言情等
- **自定义类型**: 用户可创建专属小说类型
- **类型配置**: 标签、提示词、示例作品管理
- **使用统计**: 类型使用频率跟踪
- **智能推荐**: 基于使用习惯的类型推荐

### 🤖 **AI写作助手**
- **智能续写**: 
  - 自定义续写方向和字数要求(200-5000字)
  - 实时流式输出，可随时停止
  - 完整内容预览，智能上下文感知
  - 一键复制或追加到文章
- **AI内容润色**:
  - 智能检测选中内容或整文润色
  - 专业润色类型：语法、文风、情感、逻辑
  - 自定义润色要求，个性化处理
  - 流式润色过程，实时查看效果
- **事件时间线**:
  - 支持事件编辑和删除操作
  - 悬停显示操作菜单
  - 直观的三点菜单交互

### 💬 **智能提示词库**
- **分类管理**: 大纲生成、正文创作、润色优化、对话场景等
- **专业模板**: 
  - 基础正文：标准章节内容生成
  - 对话生成：以对话为主的内容
  - 场景描写：环境氛围渲染
  - 动作情节：冲突和动作描述
  - 心理描写：内心活动刻画
  - 润色优化：语法润色、文风优化、情感增强等
- **变量系统**: 支持动态变量替换
- **智能集成**: 润色功能自动调用对应分类提示词
- **使用统计**: 提示词效果追踪
- **模板导入**: 世界观模板和格式模板一键插入

### 🌟 **世界观构建**
- **复杂设定支持**: 科幻修仙、赛博朋克等复杂世界观
- **模板化管理**: 
  - 核心设定（技术水平、社会结构、特殊机制）
  - 关键元素（重要物品、势力组织、地理环境）
  - 故事背景（历史事件、主要冲突、发展趋势）
- **一致性检查**: AI驱动的世界观一致性验证
- **格式化输出**: 标准化的世界观描述格式

### ⚙️ **系统设置**
- **API配置**: 多AI服务商支持，灵活切换
- **数据管理**: 
  - 分类导出：小说数据、提示词库、类型设置、API配置
  - 选择性导入：支持部分数据导入
  - 数据概览：存储空间使用情况
  - 安全清理：分级数据清理选项

### 📊 **Token计费管理**
- **使用统计**: 实时Token消耗跟踪
- **成本分析**: 按模型、按功能的成本分析
- **预算控制**: 使用限额设置
- **账单详情**: 详细的计费记录

## 🛠️ 技术栈

### 前端框架
- **Vue 3.3.8** - 现代响应式框架 (Composition API)
- **Element Plus 2.4.2** - 企业级UI组件库
- **Vue Router 4.2.5** - 官方路由管理
- **Pinia 2.1.7** - 新一代状态管理

### 开发工具
- **Vite 4.5.0** - 极速构建工具
- **TypeScript** - 类型安全的JavaScript
- **ESLint + Prettier** - 代码质量保证

### 编辑器与工具
- **WangEditor 5.1.23** - 专业富文本编辑器
- **Marked 9.1.6** - Markdown解析器
- **Highlight.js 11.9.0** - 代码高亮
- **Axios 1.6.0** - HTTP客户端

### AI服务集成
- **OpenAI GPT系列** - GPT-3.5/4/4o
- **Anthropic Claude** - Claude-3/3.5/4
- **Google Gemini** - Gemini-1.5/2.0-pro
- **国产大模型** - DeepSeek、通义千问、文心一言等

## 📦 快速开始

### 环境要求
```bash
Node.js >= 16.0.0
npm >= 8.0.0 或 pnpm >= 7.0.0 (推荐)
```

### 安装与运行
```bash
# 克隆仓库
git clone https://github.com/ponysb/91Writing.git
cd 91-writer

# 安装依赖
pnpm install

# 启动开发服务器
pnpm dev

# 构建生产版本
pnpm build
```

### 首次使用
1. **配置AI服务**: 点击右上角「API配置」，添加您的API密钥
2. **创建项目**: 选择小说类型，输入基本信息
3. **设定目标**: 在写作目标页面创建您的创作计划
4. **开始创作**: 使用AI辅助工具开始您的创作之旅

## ⚙️ 配置指南

### AI服务配置
支持的主要AI服务商（仅支持OpenAi接入格式）：

| 服务商 | 模型推荐 | 特点 |
|--------|----------|------|
| OpenAI | GPT-4o, GPT-4-turbo | 通用性强，创作质量高 |
| Anthropic | Claude-4-Sonnet | 长文本处理，逻辑性强 |
| Google | Gemini-2.5-pro | 多模态支持，响应速度快 |
| DeepSeek | DeepSeek-V3 | 中文优化，性价比高 |

### 推荐配置
- **新手作者**: Gemini-2.0-pro
- **专业作者**: Claude-3.5-Sonnet + GPT-4o
- **预算有限**: DeepSeek-V3 + 通义千问
- **长篇创作**: Claude-3.5-Sonnet (200K上下文)

## 🎨 使用场景

### 🌟 **长篇小说创作**
```
1. 选择类型模板 → 2. AI生成大纲 → 3. 章节式创作 → 4. 智能续写润色 → 5. 状态管理发布
```

### 🚀 **短篇快速创作**
```
1. 设定写作目标 → 2. 使用专业提示词 → 3. AI辅助续写 → 4. 内容润色优化 → 5. 一键导出
```

### ✍️ **AI辅助创作**
```
1. 编写开头内容 → 2. 设定续写方向 → 3. 流式智能续写 → 4. 选择性润色 → 5. 完善成文
```

### 🎨 **内容优化提升**
```
1. 选择待优化段落 → 2. 选择润色类型 → 3. 流式润色过程 → 4. 对比效果 → 5. 应用优化
```

### 🌍 **复杂世界观构建**
```
1. 世界观模板 → 2. 核心设定填写 → 3. AI完善细节 → 4. 一致性检查
```

### 🎯 **目标导向创作**
```
1. 制定写作计划 → 2. 设定日/周/月目标 → 3. 进度实时跟踪 → 4. 成就激励
```

## 🌟 高级功能

### 🧠 **AI创作引擎**
- **上下文感知**: 基于前文内容的智能续写
- **风格一致性**: 保持作者独特的写作风格
- **情节连贯性**: 确保故事逻辑的连续性
- **多样化输出**: 提供多个创作方案选择

### 📊 **数据分析**
- **创作习惯分析**: 最佳创作时间、效率统计
- **内容质量评估**: AI驱动的文本质量分析
- **读者反馈集成**: 支持外部反馈数据导入
- **趋势预测**: 基于数据的创作建议

### 🔧 **扩展性**
- **插件系统**: 支持第三方插件扩展
- **API开放**: 提供开发者API接口
- **主题定制**: 支持界面主题自定义
- **云端同步**: 多设备数据同步（开发中）

## 🤝 社区与支持

### 📞 联系方式
- 🐛 **Bug反馈**: [GitHub Issues](../../issues)
- 💡 **功能建议**: [GitHub Discussions](../../discussions)
- 📧 **邮箱支持**: <EMAIL>
- 🐧 **QQ交流群**: 

<img src=".\image\qrcode_1749609318081.jpg" style="zoom: 25%;" />

**微信公众号：**
<img src=".\image\qrcode_for_gh_3e35b4fbecbe_258.jpg" alt="微信公众号" style="width: 200px; height: 200px;">


### 🤝 贡献指南
我们欢迎所有形式的贡献！

**贡献类型**:
- 🐛 Bug修复与问题报告
- ✨ 新功能开发与建议
- 📝 文档完善与翻译
- 🎨 UI/UX设计优化
- 🔧 代码重构与性能优化
- 🌐 国际化支持

**贡献流程**:
1. Fork项目 → 2. 创建分支 → 3. 提交更改 → 4. 发起PR → 5. 代码审核

### 🏆 贡献者名单
感谢所有为91写作做出贡献的开发者！

## 📈 更新日志

### 🔥 **v0.7.0** (2025年7月9日) - 最新版本
#### 🚀 **v0.7.0 重大更新**

**🔧 API配置优化**
- ✅ 优化API配置新增官方默认API - 新增91写作官方API服务，按次计费，价格透明
- ✅ 自定义API配置 - 支持所有OpenAI格式的API接口
- ✅ 智能配置向导 - 分为新手和高级用户模式，操作更简单

**📢 系统功能增强**
- ✅ 增加公告弹窗和教程说明 - 新用户引导更完善，使用更简单
- ✅ 新增切换模型参数下拉框 - 支持随时切换模型，使用更灵活

**✍️ 短文创作全新升级**
- ✅ 短篇小说改为短文创作 - 功能更全面，支持多种短文类型
- ✅ 新增短文写作及配置 - 提供更多创作选项和个性化设置
- ✅ 优化短篇小说ui和逻辑 - 界面更美观，操作更流畅

**🛠️ 系统优化**
- ✅ 修复若干bug问题 - 提升系统稳定性和用户体验

### 🎉 **v0.6.0** (2025年6月26日)
#### 🚀 **短篇小说功能全面升级**
- ✅ 短篇小说新增续写功能 - 支持自定义续写方向和字数设置
- ✅ 短篇小说选文优化功能重构 - 可以优化完成之后一键插入
- ✅ AI正文编辑器修复部分bug问题 - 提升编辑体验稳定性

### 🎉 **v0.5.0** (2025年6月24日)
#### 🚀 **上下文内容功能全面升级**
- 模型配置预设模型重新梳理
- 短篇小说部分API兼容问题bug修复
- Ai上下文连贯性改为可以手动选择多章，默认自动关联前两章
- 小说无法导出bug修复
- 若干功能bug修复


### 🎉 **v0.4.0** (2025年6月22日)
#### 🆕 **智能编辑体验全面升级**

**✍️ AI续写功能重磅登场**
- ✅ 智能续写对话框 - 左右分栏布局，配置区+结果展示
- ✅ 自定义续写方向 - 可描述具体续写要求和情节发展
- ✅ 灵活字数控制 - 支持200-5000字范围，滑块精确调节
- ✅ 当前内容预览 - 完整显示现有内容，支持滚动查看
- ✅ 流式续写体验 - 实时观看AI创作过程，可随时停止
- ✅ 智能内容管理 - 一键复制结果或直接追加到文章

**🎨 AI润色功能深度优化**
- ✅ 智能内容检测 - 自动识别选中内容或整文润色模式
- ✅ 专业润色类型 - 语法润色、文风优化、情感增强、逻辑梳理
- ✅ 提示词库集成 - 动态获取"润色优化"分类提示词
- ✅ 自定义润色要求 - 支持个性化润色指令输入
- ✅ 双模式处理 - 选择内容替换/整文替换智能判断
- ✅ 流式输出优化 - 实时显示润色过程和最终效果

**📋 章节状态管理系统**
- ✅ 三状态管理 - 草稿(橙色)/完成(绿色)/发表(蓝色)
- ✅ 智能状态切换 - 编辑器头部下拉菜单快速修改
- ✅ 状态同步显示 - 章节列表自动更新状态标签
- ✅ 默认状态优化 - 新建章节默认为草稿状态

**📅 事件时间线增强**
- ✅ 完整编辑功能 - 支持事件的编辑和删除操作
- ✅ 悬停操作菜单 - 鼠标悬停显示操作按钮
- ✅ 直观交互设计 - 三点菜单包含编辑/删除选项

#### 🔧 **用户体验持续优化**
- ✅ 智能章节选择 - 进入编辑模块自动选中第一章节
- ✅ 删除后自动切换 - 删除当前章节后自动选择剩余首章
- ✅ 新增章节自动选择 - 创建章节后立即进入编辑状态
- ✅ 路由状态重置 - 切换小说时正确重置编辑状态

#### 🛠️ **界面与交互改进**
- ✅ 弹窗布局优化 - 续写/润色弹窗尺寸和布局调整
- ✅ 内容显示完整 - 续写配置显示完整内容而非概要
- ✅ 菜单选项精简 - 移除章节列表中多余的AI优化选项
- ✅ 提示词分类重命名 - "润色"更名为"润色优化"

#### 🐛 **问题修复与稳定性**
- ✅ 编译错误修复 - 解决函数重复声明问题
- ✅ 运行时错误修复 - 修复编辑器API调用错误
- ✅ 提示词选择修复 - 新旧对话框选择功能分离
- ✅ 样式布局修复 - 解决组件超出弹窗边界问题

#### 🎯 **开发者体验优化**
- ✅ 代码结构优化 - 功能模块化，提高代码可维护性
- ✅ 错误处理增强 - 完善异常捕获和用户提示
- ✅ 性能优化 - 减少不必要的重渲染和计算
- ✅ 文档更新 - 系统设置页面版本信息同步更新

### 🚀 **v0.3.0** (2025年1月)
#### 🆕 **三大核心模块重磅上线**

**📖 短篇小说模块**
- ✅ 智能模板系统 - 6大专业模板（都市、玄幻、言情、悬疑、科幻、通用）
- ✅ 提示词选择器 - 可选择、编辑和自定义提示词模板
- ✅ 变量自动填充 - 智能填充小说信息、角色设定、世界观等变量
- ✅ 配置管理系统 - 题材、情节、氛围、时代等创作要素管理
- ✅ 富文本编辑器 - 支持选段优化和AI助手对话
- ✅ 实时字数统计 - 创作进度实时跟踪

**🔧 智能工具库（10大专业工具）**
- ✅ 细纲生成器 - AI辅助生成详细章节大纲
- ✅ 角色生成器 - 支持1-15个角色批量生成，含详细背景设定
- ✅ 脑洞生成器 - 批量生成创意点子（3/5/8/10个选项）
- ✅ 爆款书名生成器 - 支持5-20个书名批量生成，含创意说明
- ✅ 爆款题材生成器 - 发现热门创作方向（3/5/8/10个选项）
- ✅ 宏大世界观生成器 - 构建完整的故事世界框架
- ✅ 金手指生成器 - 为角色设计独特的能力系统
- ✅ 黄金开篇生成器 - 为不同题材创作引人入胜的开头
- ✅ 简介生成器 - 生成吸引读者的作品简介
- ✅ 冲突生成器 - 设计戏剧性的故事冲突点

**📚 拆书分析模块**
- ✅ 多格式文档导入 - 支持TXT、DOCX格式，智能解析章节结构
- ✅ AI深度分析引擎 - 5种分析维度全面解析写作技法
- ✅ 综合分析 - 全方位写作技法、结构特点、创作亮点分析
- ✅ 结构分析 - 章节布局、情节推进、叙事节奏专业解析
- ✅ 人物分析 - 角色塑造、性格刻画、关系处理深度分析
- ✅ 语言分析 - 文风特色、修辞手法、表达技巧分析
- ✅ 情节分析 - 冲突设计、悬念营造、转折处理分析
- ✅ 拆书参考库 - 分析结果保存管理，支持学习应用

#### 🔧 **功能优化升级**
- ✅ 提示词库系统升级 - 新增短篇小说分类，支持变量填充
- ✅ 工具集成优化 - 所有工具支持提示词模板选择和编辑
- ✅ 素材库整合 - 工具生成内容可保存到素材库
- ✅ 小说信息传递 - 智能传递小说名字、角色信息、章节内容
- ✅ 界面交互优化 - 更流畅的用户体验和操作流程
- ✅ 数据管理增强 - 支持新模块数据的导入导出

#### 🎯 **使用场景扩展**
- ✅ 短篇小说快速创作流程
- ✅ 专业工具辅助长篇创作
- ✅ 优秀作品学习分析
- ✅ 创意灵感批量生成
- ✅ 目标导向的系统化创作

### 🎯 **v0.2.0** (2024年12月)
- ✅ 所有0.1.0的功能重构
- ✅ 新增提示词库管理
- ✅ 新增分类管理
- ✅ 新增写作目标设置
- ✅ 新增API调用token计费管理
- ✅ 新增首页仪表盘

### 🎯 **v0.1.0** (2024年11月)
- ✅ 基础编辑器功能
- ✅ 基础Ai生成正文
- ✅ 基础写作模版、小说大纲智能生成
- ✅ 人物管理、世界观设定、写作进度
- ✅ 灵感记录集
- ✅ 文章摘要
- ✅ 文章统计
- ✅ 语料库
- ✅ API配置


## 📄 开源协议

本项目基于 **MIT License** 开源协议发布。详见 [LICENSE](LICENSE) 文件。

## 🙏 致谢

感谢以下优秀的开源项目：

| 项目 | 作用 | 官网 |
|------|------|------|
| Vue.js | 前端框架 | https://vuejs.org/ |
| Element Plus | UI组件库 | https://element-plus.org/ |
| Vite | 构建工具 | https://vitejs.dev/ |
| WangEditor | 富文本编辑器 | https://www.wangeditor.com/ |

特别感谢所有AI服务提供商为创作者提供的强大技术支持。

---

<div align="center">

**🌟 如果这个项目对您有帮助，请给个Star支持一下！**

[![Star History Chart](https://api.star-history.com/svg?repos=your-username/91-writer&type=Date)](https://star-history.com/#your-username/91-writer&Date)

</div>

---

<img src=".\image\微信截图_20250619121528.png" />
<img src=".\image\微信截图_20250619121537.png" />
<img src=".\image\微信截图_20250619121547.png" />
<img src=".\image\微信截图_20250619121556.png" />
<img src=".\image\微信截图_20250621205520.png" />
<img src=".\image\微信截图_20250621205549.png" />


*最后更新: 2025年1月20日*