package com.writing.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * API配置实体类
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("api_configs")
public class ApiConfig {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    @TableField("user_id")
    private Long userId;

    @TableField("name")
    private String name;

    @TableField("type")
    private String type;

    @TableField("api_key")
    private String apiKey;

    @TableField("base_url")
    private String baseUrl;

    @TableField("selected_model")
    private String selectedModel;

    @TableField("max_tokens")
    private Integer maxTokens;

    @TableField("unlimited_tokens")
    private Integer unlimitedTokens;

    @TableField("temperature")
    private BigDecimal temperature;

    @TableField("top_p")
    private BigDecimal topP;

    @TableField("frequency_penalty")
    private BigDecimal frequencyPenalty;

    @TableField("presence_penalty")
    private BigDecimal presencePenalty;

    @TableField("timeout")
    private Integer timeout;

    @TableField("stream_mode")
    private Integer streamMode;

    @TableField("retry_count")
    private Integer retryCount;

    @TableField("custom_headers")
    private String customHeaders;

    @TableField("status")
    private String status;

    @TableField("is_active")
    private Integer isActive;

    @TableField(value = "created_at", fill = FieldFill.INSERT)
    private LocalDateTime createdAt;

    @TableField(value = "updated_at", fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updatedAt;

    @TableLogic
    @TableField("deleted")
    private Integer deleted;
}
