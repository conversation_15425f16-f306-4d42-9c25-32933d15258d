package com.writing.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.writing.common.Result;
import com.writing.entity.ApiConfig;
import com.writing.service.ApiConfigService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * API配置控制器
 */
@RestController
@RequestMapping("/api-configs")
@RequiredArgsConstructor
public class ApiConfigController {

    private final ApiConfigService apiConfigService;

    /**
     * 分页获取用户API配置列表
     */
    @GetMapping
    public Result<IPage<ApiConfig>> getApiConfigs(
            @RequestAttribute("userId") Long userId,
            @RequestParam(defaultValue = "1") int current,
            @RequestParam(defaultValue = "10") int size) {

        IPage<ApiConfig> configs = apiConfigService.getUserApiConfigs(userId, current, size);
        return Result.success(configs);
    }

    /**
     * 获取用户所有API配置列表
     */
    @GetMapping("/list")
    public Result<List<ApiConfig>> getApiConfigList(@RequestAttribute("userId") Long userId) {
        List<ApiConfig> configs = apiConfigService.getUserApiConfigList(userId);
        return Result.success(configs);
    }

    /**
     * 获取API配置详情
     */
    @GetMapping("/{configId}")
    public Result<ApiConfig> getApiConfig(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long configId) {

        ApiConfig config = apiConfigService.getUserApiConfig(userId, configId);
        return Result.success(config);
    }

    /**
     * 创建API配置
     */
    @PostMapping
    public Result<ApiConfig> createApiConfig(
            @RequestAttribute("userId") Long userId,
            @RequestBody ApiConfig apiConfig) {

        apiConfig.setUserId(userId);
        ApiConfig created = apiConfigService.createApiConfig(apiConfig);
        return Result.success("API配置创建成功", created);
    }

    /**
     * 更新API配置
     */
    @PutMapping("/{configId}")
    public Result<ApiConfig> updateApiConfig(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long configId,
            @RequestBody ApiConfig apiConfig) {

        apiConfig.setId(configId);
        apiConfig.setUserId(userId);
        ApiConfig updated = apiConfigService.updateApiConfig(apiConfig);
        return Result.success("API配置更新成功", updated);
    }

    /**
     * 删除API配置
     */
    @DeleteMapping("/{configId}")
    public Result<Void> deleteApiConfig(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long configId) {

        apiConfigService.deleteApiConfig(userId, configId);
        return Result.success();
    }

    /**
     * 激活API配置
     */
    @PostMapping("/{configId}/activate")
    public Result<Void> activateApiConfig(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long configId) {

        apiConfigService.activateApiConfig(userId, configId);
        return Result.success();
    }

    /**
     * 测试API配置连接
     */
    @PostMapping("/{configId}/test")
    public Result<String> testApiConfig(
            @RequestAttribute("userId") Long userId,
            @PathVariable Long configId) {

        String result = apiConfigService.testApiConfig(userId, configId);
        return Result.success("连接测试成功", result);
    }

    /**
     * 获取当前激活的API配置
     */
    @GetMapping("/active")
    public Result<ApiConfig> getActiveApiConfig(@RequestAttribute("userId") Long userId) {
        ApiConfig config = apiConfigService.getActiveApiConfig(userId);
        return Result.success(config);
    }

    /**
     * 批量保存API配置
     */
    @PostMapping("/batch")
    public Result<List<ApiConfig>> batchSaveApiConfigs(
            @RequestAttribute("userId") Long userId,
            @RequestBody List<ApiConfig> apiConfigs) {

        // 设置用户ID
        apiConfigs.forEach(config -> config.setUserId(userId));
        List<ApiConfig> saved = apiConfigService.batchSaveApiConfigs(apiConfigs);
        return Result.success("批量保存成功", saved);
    }
}
