package com.writing.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.WorldSetting;

import java.util.List;

/**
 * 世界观设定Service接口
 */
public interface WorldSettingService extends IService<WorldSetting> {

    /**
     * 获取小说的世界观设定列表
     */
    List<WorldSetting> getWorldSettingsByNovelId(Long novelId);

    /**
     * 根据ID获取世界观设定
     */
    WorldSetting getWorldSettingById(Long worldSettingId, Long novelId);

    /**
     * 创建世界观设定
     */
    WorldSetting createWorldSetting(WorldSetting worldSetting);

    /**
     * 更新世界观设定
     */
    WorldSetting updateWorldSetting(WorldSetting worldSetting);

    /**
     * 删除世界观设定
     */
    boolean deleteWorldSetting(Long worldSettingId, Long novelId);
}
