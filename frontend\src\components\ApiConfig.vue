<template>
  <div class="api-config">
    <el-card class="config-card">
      <template #header>
        <div class="card-header">
          <span>API配置管理</span>
          <div class="header-actions">
            <el-tag :type="isApiConfigured ? 'success' : 'danger'" size="small">
              {{ isApiConfigured ? '已配置' : '未配置' }}
            </el-tag>
            <el-button type="primary" size="small" @click="showCreateDialog = true">
              <el-icon><Plus /></el-icon>
              新建配置
            </el-button>
          </div>
        </div>
      </template>

      <!-- 配置列表 -->
      <div class="config-list-section" v-if="savedConfigs.length > 0">
        <h4>💾 已保存的配置</h4>
        <div class="config-cards">
          <div
            v-for="config in savedConfigs"
            :key="config.id"
            class="config-card-item"
            :class="{ 'active': config.isActive }"
          >
            <div class="config-card-header">
              <div class="config-info">
                <h5>{{ config.name }}</h5>
                <p>{{ config.type === 'custom' ? '自定义配置' : '官方配置' }}</p>
              </div>
              <div class="config-actions">
                <el-tag v-if="config.isActive" type="success" size="small">当前使用</el-tag>
                <el-button
                  v-else
                  type="primary"
                  size="small"
                  @click="activateConfig(config.id)"
                  :loading="activating === config.id"
                >
                  切换使用
                </el-button>
              </div>
            </div>
            <div class="config-card-body">
              <div class="config-details">
                <p><strong>模型:</strong> {{ config.selectedModel }}</p>
                <p><strong>地址:</strong> {{ config.baseUrl }}</p>
                <p><strong>状态:</strong>
                  <el-tag :type="getStatusType(config.status)" size="small">
                    {{ getStatusText(config.status) }}
                  </el-tag>
                </p>
              </div>
              <div class="config-card-actions">
                <el-button size="small" @click="editConfig(config)">编辑</el-button>
                <el-button size="small" @click="testConfig(config.id)" :loading="testing === config.id">测试</el-button>
                <el-button
                  size="small"
                  type="danger"
                  @click="deleteConfig(config.id)"
                  :disabled="config.isActive"
                >
                  删除
                </el-button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 - 左右分栏 -->
      <div class="config-main-content">
        <!-- 左侧：配置说明 -->
        <div class="config-tips-panel">
          <!-- 自定义配置说明 -->
          <div class="config-tips custom-tips">
            <h4>⚙️ 配置说明</h4>
            <div class="tips-content">
              <p><strong>支持OpenAI格式API</strong>，兼容大部分AI模型服务。</p>

              <div class="params-info">
                <h5>参数说明：</h5>
                <ul>
                  <li><strong>配置名称</strong> - 便于识别的配置名称</li>
                  <li><strong>API地址</strong> - 您的API服务地址</li>
                  <li><strong>API密钥</strong> - 身份验证密钥</li>
                  <li><strong>模型选择</strong> - 支持自定义模型名称</li>
                  <li><strong>Token限制</strong> - 控制生成长度</li>
                  <li><strong>创造性</strong> - 0保守，1创新</li>
                </ul>
              </div>

              <div class="supported-apis">
                <h5>特殊说明：</h5>
                <ul>
                  <li>支持多个配置保存和快速切换</li>
                  <li>配置保存在云端，多设备同步</li>
                  <li>支持本地部署大模型，如ollama、llmstudio等</li>
                </ul>
              </div>

              <div class="usage-steps">
                 <h5>使用教程：</h5>
                 <ol>
                   <li><a href="https://www.bilibili.com/video/BV1keKgzaER2" target="_blank">API配置教程</a></li>
                   <li><a href="https://www.bilibili.com/video/BV1AYKgzAEne" target="_blank">本地部署及线上部署教程</a></li>
                  </ol>
               </div>

              <div class="tips-note">
                <p>💡 建议先测试连接再保存配置</p>
              </div>
            </div>
          </div>
        </div>

        <!-- 右侧：当前配置信息 -->
        <div class="config-form-panel" v-if="!showCreateDialog && !editingConfig">
          <div class="current-config-info">
            <el-alert
              title="当前配置信息"
              type="info"
              :closable="false"
              show-icon
            >
              <template #default>
                <div v-if="activeConfig">
                  <p><strong>配置名称:</strong> {{ activeConfig.name }}</p>
                  <p><strong>模型:</strong> {{ activeConfig.selectedModel }}</p>
                  <p><strong>API地址:</strong> {{ activeConfig.baseUrl }}</p>
                  <p><strong>状态:</strong>
                    <el-tag :type="getStatusType(activeConfig.status)" size="small">
                      {{ getStatusText(activeConfig.status) }}
                    </el-tag>
                  </p>
                </div>
                <div v-else>
                  <p>暂无激活的配置，请创建或激活一个配置。</p>
                </div>
              </template>
            </el-alert>
          </div>
        </div>

        <!-- 配置表单 -->
        <div class="config-form-panel" v-if="showCreateDialog || editingConfig">
          <div class="custom-config">
            <el-alert
              :title="editingConfig ? '编辑配置' : '新建配置'"
              type="warning"
              :closable="false"
              show-icon
            >
              <template #default>
                {{ editingConfig ? '修改现有API配置' : '创建新的API配置' }}
              </template>
            </el-alert>

            <el-form :model="configForm" label-width="80px" size="small" class="config-form">
              <el-form-item label="配置名称" required>
                <el-input
                  v-model="configForm.name"
                  placeholder="请输入配置名称"
                  clearable
                />
              </el-form-item>

              <el-form-item label="API密钥" required>
                <el-input
                  v-model="configForm.apiKey"
                  type="password"
                  placeholder="请输入API密钥"
                  show-password
                  clearable
                />
              </el-form-item>

              <el-form-item label="API地址" required>
                <el-input
                  v-model="configForm.baseUrl"
                  placeholder="https://api.openai.com/v1"
                  clearable
                />
              </el-form-item>

              <el-form-item label="模型选择">
                <el-select
                  v-model="configForm.selectedModel"
                  placeholder="选择模型"
                  filterable
                  allow-create
                >
                  <el-option
                    v-for="model in availableModels"
                    :key="model.id"
                    :label="model.name"
                    :value="model.id"
                  >
                    <span>{{ model.name }}</span>
                    <span style="float: right; color: #8492a6; font-size: 12px">
                      {{ model.description }}
                    </span>
                  </el-option>
                </el-select>
              </el-form-item>

              <el-form-item label="自定义模型">
                <div class="custom-model-input">
                  <el-input
                    v-model="customModelInput"
                    placeholder="输入自定义模型名称"
                    @keyup.enter="addCustomModel"
                  />
                  <el-button @click="addCustomModel" type="primary" size="small">添加</el-button>
                </div>
                <div v-if="customModels.length > 0" class="custom-models-list">
                  <el-tag
                    v-for="model in customModels"
                    :key="model.id"
                    closable
                    @close="removeCustomModel(model.id)"
                    size="small"
                    style="margin-right: 8px; margin-bottom: 4px;"
                  >
                    {{ model.name }}
                  </el-tag>
                </div>
              </el-form-item>

              <el-form-item label="最大Token">
                <div class="max-tokens-control">
                  <el-checkbox v-model="configForm.unlimitedTokens" @change="handleUnlimitedTokensChange">
                    无限制Token
                  </el-checkbox>
                  <el-input-number
                    v-if="!configForm.unlimitedTokens"
                    v-model="configForm.maxTokens"
                    :min="1"
                    :max="10000000"
                    :step="1000"
                    style="width: 100%"
                  />
                </div>
              </el-form-item>

              <el-form-item label="创造性">
                <el-slider
                  v-model="configForm.temperature"
                  :min="0"
                  :max="1"
                  :step="0.1"
                  :format-tooltip="formatTemperature"
                  show-tooltip
                />
              </el-form-item>

              <el-form-item>
                <el-button type="primary" @click="saveConfig" :loading="saving">
                  {{ saving ? '保存中...' : (editingConfig ? '更新配置' : '保存配置') }}
                </el-button>
                <el-button @click="testConnection" :loading="testing">
                  测试连接
                </el-button>
                <el-button @click="cancelEdit">取消</el-button>
              </el-form-item>
            </el-form>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { Plus } from '@element-plus/icons-vue'
import { useNovelStore } from '../stores/novel.js'
import apiConfigService from '../services/apiConfigService.js'

const store = useNovelStore()

// 响应式数据
const savedConfigs = ref([])
const activeConfig = ref(null)
const showCreateDialog = ref(false)
const editingConfig = ref(null)
const saving = ref(false)
const testing = ref(null)
const activating = ref(null)
const customModelInput = ref('')
const customModels = ref([])

// 配置表单
const configForm = reactive({
  id: null,
  name: '',
  type: 'custom',
  apiKey: '',
  baseUrl: 'https://api.openai.com/v1',
  selectedModel: 'gpt-3.5-turbo',
  maxTokens: 2000000,
  unlimitedTokens: false,
  temperature: 0.7
})



// 自定义配置可选模型
const defaultModels = [
  {
    id: 'deepseek-reasoner',
    name: 'deepseek-r1',
    description: 'deepseek-r1'
  },
  {
    id: 'deepseek-chat',
    name: 'deepseek-v3',
    description: 'deepseek-v3'
  },
  {
    id: 'claude-3.7-sonnet',
    name: 'claude-3.7-sonnet',
    description: 'claude-3.7-sonnet'
  },
  {
    id: 'claude-4-sonnet',
    name: 'claude-4-sonnet',
    description: 'claude-4-sonnet'
  },
  {
    id: 'gemini-2.5-pro-preview-05-06',
    name: 'gemini-2.5-pro-preview-05-06',
    description: 'gemini-2.5-pro-preview-05-06'
  }
]

const availableModels = computed(() => {
  return [...defaultModels, ...customModels.value]
})

const isApiConfigured = computed(() => {
  return activeConfig.value && activeConfig.value.apiKey
})

const formatTemperature = (value) => {
  if (value <= 0.3) return '保守'
  if (value <= 0.7) return '平衡'
  return '创新'
}

const getStatusType = (status) => {
  const types = {
    connected: 'success',
    disconnected: 'info',
    connecting: 'warning',
    error: 'danger'
  }
  return types[status] || 'info'
}

const getStatusText = (status) => {
  const texts = {
    connected: '已连接',
    disconnected: '未连接',
    connecting: '连接中',
    error: '连接错误'
  }
  return texts[status] || '未知'
}



// 配置管理方法
const loadConfigs = async () => {
  try {
    const result = await apiConfigService.getApiConfigList()
    savedConfigs.value = result.data || []

    // 获取当前激活的配置
    const activeResult = await apiConfigService.getActiveApiConfig()
    activeConfig.value = activeResult.data

    // 更新store状态
    if (activeConfig.value) {
      store.updateApiConfig(activeConfig.value, 'custom')
    }
  } catch (error) {
    console.error('加载配置失败:', error)
    ElMessage.error('加载配置失败')
  }
}

const activateConfig = async (configId) => {
  activating.value = configId
  try {
    await apiConfigService.activateApiConfig(configId)
    ElMessage.success('配置切换成功')
    await loadConfigs() // 重新加载配置
  } catch (error) {
    console.error('激活配置失败:', error)
    ElMessage.error('配置切换失败')
  } finally {
    activating.value = null
  }
}

const editConfig = (config) => {
  editingConfig.value = config
  showCreateDialog.value = true

  // 填充表单
  Object.assign(configForm, {
    id: config.id,
    name: config.name,
    type: config.type,
    apiKey: config.apiKey,
    baseUrl: config.baseUrl,
    selectedModel: config.selectedModel,
    maxTokens: config.maxTokens,
    unlimitedTokens: config.unlimitedTokens === 1,
    temperature: parseFloat(config.temperature)
  })
}

const deleteConfig = async (configId) => {
  try {
    await ElMessageBox.confirm(
      '确定要删除这个配置吗？此操作不可恢复。',
      '确认删除',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    await apiConfigService.deleteApiConfig(configId)
    ElMessage.success('配置删除成功')
    await loadConfigs()
  } catch (error) {
    if (error !== 'cancel') {
      console.error('删除配置失败:', error)
      ElMessage.error('删除配置失败')
    }
  }
}

const testConfig = async (configId) => {
  testing.value = configId
  try {
    await apiConfigService.testApiConfig(configId)
    ElMessage.success('连接测试成功')
    await loadConfigs() // 重新加载以更新状态
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = null
  }
}

const handleUnlimitedTokensChange = () => {
  if (configForm.unlimitedTokens) {
    configForm.maxTokens = null
  } else {
    configForm.maxTokens = 2000000
  }
}

const addCustomModel = () => {
  const modelName = customModelInput.value.trim()
  if (!modelName) return

  const exists = availableModels.value.some(model => model.id === modelName)
  if (exists) {
    ElMessage.warning('该模型已存在')
    return
  }

  customModels.value.push({
    id: modelName,
    name: modelName,
    description: '自定义模型'
  })

  customModelInput.value = ''
  ElMessage.success('自定义模型添加成功')
  saveCustomModels()
}

const removeCustomModel = (modelId) => {
  const index = customModels.value.findIndex(model => model.id === modelId)
  if (index > -1) {
    customModels.value.splice(index, 1)

    if (configForm.selectedModel === modelId) {
      configForm.selectedModel = 'gpt-3.5-turbo'
    }

    ElMessage.success('自定义模型删除成功')
    saveCustomModels()
  }
}

const saveCustomModels = () => {
  localStorage.setItem('customModels', JSON.stringify(customModels.value))
}

const loadCustomModels = () => {
  const saved = localStorage.getItem('customModels')
  if (saved) {
    try {
      customModels.value = JSON.parse(saved)
    } catch (error) {
      console.error('加载自定义模型失败:', error)
    }
  }
}

const saveConfig = async () => {
  if (!configForm.name || !configForm.apiKey || !configForm.baseUrl) {
    ElMessage.warning('请填写完整的配置信息')
    return
  }

  saving.value = true
  try {
    const configData = {
      ...configForm,
      unlimitedTokens: configForm.unlimitedTokens ? 1 : 0
    }

    if (editingConfig.value) {
      // 更新配置
      await apiConfigService.updateApiConfig(configForm.id, configData)
      ElMessage.success('配置更新成功')
    } else {
      // 创建新配置
      await apiConfigService.createApiConfig(configData)
      ElMessage.success('配置创建成功')
    }

    await loadConfigs()
    cancelEdit()
  } catch (error) {
    console.error('保存配置失败:', error)
    ElMessage.error('保存配置失败')
  } finally {
    saving.value = false
  }
}

const testConnection = async () => {
  if (!configForm.apiKey || !configForm.baseUrl) {
    ElMessage.warning('请先填写API密钥和地址')
    return
  }

  testing.value = true
  try {
    // 如果是编辑模式且配置已保存，直接测试
    if (editingConfig.value && configForm.id) {
      await apiConfigService.testApiConfig(configForm.id)
    } else {
      // 使用临时测试接口，不创建实际配置
      const tempConfig = {
        ...configForm,
        unlimitedTokens: configForm.unlimitedTokens ? 1 : 0
      }
      // 直接调用测试接口，不保存配置
      await apiConfigService.testApiConfigDirect(tempConfig)
    }

    ElMessage.success('连接测试成功')
  } catch (error) {
    console.error('测试连接失败:', error)
    ElMessage.error('连接测试失败')
  } finally {
    testing.value = false
  }
}

const cancelEdit = () => {
  showCreateDialog.value = false
  editingConfig.value = null

  // 重置表单
  Object.assign(configForm, {
    id: null,
    name: '',
    type: 'custom',
    apiKey: '',
    baseUrl: 'https://api.openai.com/v1',
    selectedModel: 'gpt-3.5-turbo',
    maxTokens: 2000000,
    unlimitedTokens: false,
    temperature: 0.7
  })
}

onMounted(() => {
  loadCustomModels()
  loadConfigs()
})
</script>

<style scoped>
.api-config {
  padding: 20px;
  max-width: 100%;
}

.config-card {
  max-width: 1600px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.config-list-section {
  margin-bottom: 24px;
}

.config-list-section h4 {
  margin: 0 0 16px 0;
  color: #303133;
  font-size: 16px;
  font-weight: 600;
}

.config-cards {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 16px;
}

.config-card-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 16px;
  background: #fff;
  transition: all 0.3s ease;
}

.config-card-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.config-card-item.active {
  border-color: #67c23a;
  background: #f0f9ff;
}

.config-card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.config-info h5 {
  margin: 0 0 4px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
}

.config-info p {
  margin: 0;
  color: #909399;
  font-size: 12px;
}

.config-details {
  margin-bottom: 12px;
}

.config-details p {
  margin: 4px 0;
  color: #606266;
  font-size: 13px;
}

.config-card-actions {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

.current-config-info {
  padding: 16px 0;
}

.config-type-selector {
  margin-bottom: 20px;
  text-align: center;
}

/* 主要内容区域 - 左右分栏布局 */
.config-main-content {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 32px;
  align-items: start;
}

/* 左侧配置说明面板 */
.config-tips-panel {
  min-height: 400px;
}

.config-tips {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 20px;
  height: 100%;
}

.config-tips.official-tips {
  background: #e8f4fd;
  border-color: #b3d9f7;
}

.config-tips.custom-tips {
  background: #fef4e8;
  border-color: #f7d9b3;
}

.config-tips h4 {
  margin: 0 0 12px 0;
  color: #2c3e50;
  font-size: 16px;
  font-weight: 600;
}

.config-tips h5 {
  margin: 16px 0 8px 0;
  color: #34495e;
  font-size: 14px;
  font-weight: 600;
}

.tips-content p {
  margin: 0 0 12px 0;
  color: #5a6c7d;
  line-height: 1.5;
}

.tips-content ul,
.tips-content ol {
  margin: 8px 0;
  padding-left: 20px;
}

.tips-content li {
  margin-bottom: 4px;
  color: #5a6c7d;
  line-height: 1.4;
  font-size: 13px;
}

.purchase-info {
  margin-top: 16px;
  padding: 12px;
  background: white;
  border: 1px solid #ddd;
  border-radius: 6px;
  text-align: center;
}

.purchase-info p {
  margin: 0 0 8px 0;
  font-size: 13px;
}

.tips-note {
  margin-top: 16px;
  padding: 8px 12px;
  background: #fff3cd;
  border: 1px solid #ffeaa7;
  border-radius: 4px;
}

.tips-note p {
  margin: 0;
  font-size: 12px;
  color: #856404;
}

/* 右侧配置表单面板 */
.config-form-panel {
  min-height: 400px;
}

.config-form {
  margin-top: 16px;
  padding: 0 8px;
}

.model-option {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.model-name {
  font-weight: 500;
}

.model-price {
  color: #F56C6C;
  font-size: 12px;
  font-weight: 600;
}

.model-description {
  color: #909399;
  font-size: 12px;
  margin-top: 2px;
}

.form-tip {
  font-size: 12px;
  color: #909399;
  margin-top: 4px;
}

.custom-model-input {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.custom-models-list {
  margin-top: 8px;
}

.max-tokens-control {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 响应式布局 */
@media (max-width: 900px) {
  .config-main-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .config-tips-panel,
  .config-form-panel {
    min-height: auto;
  }
  
  .config-card {
    max-width: 100%;
  }
}

@media (max-width: 1200px) and (min-width: 901px) {
  .config-main-content {
    grid-template-columns: 1fr 1fr;
    gap: 24px;
  }
}

:deep(.el-form-item__label) {
  font-weight: 500;
}

:deep(.el-slider__runway) {
  margin: 16px 0;
}

:deep(.el-radio-button__inner) {
  padding: 10px 20px;
  font-weight: 500;
}

:deep(.el-alert) {
  margin-bottom: 16px;
}

.official-config, .custom-config {
  min-height: 350px;
}
</style>