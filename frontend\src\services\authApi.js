import apiClient from './apiClient'

/**
 * 认证相关API
 */
export const authApi = {
  
  /**
   * 用户登录
   */
  login(username, password) {
    return apiClient.post('/auth/login', {
      username,
      password
    })
  },
  
  /**
   * 用户注册
   */
  register(username, email, password) {
    return apiClient.post('/auth/register', {
      username,
      email,
      password
    })
  },
  
  /**
   * 获取当前用户信息
   */
  getCurrentUser() {
    return apiClient.get('/auth/me')
  },

  /**
   * 更新用户信息
   */
  updateProfile(profileData) {
    return apiClient.put('/auth/profile', profileData)
  },

  /**
   * 退出登录
   */
  logout() {
    // 清除本地存储的token和用户信息
    localStorage.removeItem('token')
    localStorage.removeItem('user')
    return Promise.resolve()
  }
}

/**
 * 认证工具函数
 */
export const authUtils = {
  
  /**
   * 保存登录信息
   */
  saveAuthData(token, user) {
    localStorage.setItem('token', token)
    localStorage.setItem('user', JSON.stringify(user))
  },
  
  /**
   * 获取token
   */
  getToken() {
    return localStorage.getItem('token')
  },
  
  /**
   * 获取用户信息
   */
  getUser() {
    const userStr = localStorage.getItem('user')
    return userStr ? JSON.parse(userStr) : null
  },
  
  /**
   * 检查是否已登录
   */
  isLoggedIn() {
    return !!this.getToken()
  },
  
  /**
   * 清除认证信息
   */
  clearAuthData() {
    localStorage.removeItem('token')
    localStorage.removeItem('user')
  }
}
