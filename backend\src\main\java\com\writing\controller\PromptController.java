package com.writing.controller;

import com.writing.common.Result;
import com.writing.entity.Prompt;
import com.writing.service.PromptService;

import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import java.util.List;

/**
 * 提示词控制器
 */
@RestController
@RequestMapping("/prompts")
@RequiredArgsConstructor
public class PromptController {

    private final PromptService promptService;

    /**
     * 获取用户的所有提示词
     */
    @GetMapping
    public Result<List<Prompt>> getPrompts(@RequestParam(required = false) String category,
                                           HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        try {
            List<Prompt> prompts = promptService.getPromptsByUserId(userId, category);
            return Result.success(prompts);
        } catch (Exception e) {
            return Result.error("获取提示词列表失败: " + e.getMessage());
        }
    }

    /**
     * 获取提示词详情
     */
    @GetMapping("/{promptId}")
    public Result<Prompt> getPrompt(@PathVariable Long promptId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        try {
            Prompt prompt = promptService.getPromptById(promptId, userId);
            if (prompt == null) {
                return Result.error("提示词不存在");
            }
            return Result.success(prompt);
        } catch (Exception e) {
            return Result.error("获取提示词详情失败: " + e.getMessage());
        }
    }

    /**
     * 创建提示词
     */
    @PostMapping
    public Result<Prompt> createPrompt(@RequestBody Prompt prompt, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        try {
            prompt.setUserId(userId);
            Prompt createdPrompt = promptService.createPrompt(prompt);
            return Result.success(createdPrompt);
        } catch (Exception e) {
            return Result.error("创建提示词失败: " + e.getMessage());
        }
    }

    /**
     * 更新提示词
     */
    @PutMapping("/{promptId}")
    public Result<Prompt> updatePrompt(@PathVariable Long promptId,
                                     @RequestBody Prompt prompt,
                                     HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        try {
            prompt.setId(promptId);
            prompt.setUserId(userId);
            Prompt updatedPrompt = promptService.updatePrompt(prompt, userId);
            if (updatedPrompt == null) {
                return Result.error("提示词不存在或无权限修改");
            }
            return Result.success(updatedPrompt);
        } catch (Exception e) {
            return Result.error("更新提示词失败: " + e.getMessage());
        }
    }

    /**
     * 删除提示词
     */
    @DeleteMapping("/{promptId}")
    public Result<Void> deletePrompt(@PathVariable Long promptId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        try {
            boolean deleted = promptService.deletePrompt(promptId, userId);
            if (!deleted) {
                return Result.error("提示词不存在或无权限删除");
            }
            return Result.success();
        } catch (Exception e) {
            return Result.error("删除提示词失败: " + e.getMessage());
        }
    }

    /**
     * 增加提示词使用次数
     */
    @PostMapping("/{promptId}/use")
    public Result<Void> incrementUsageCount(@PathVariable Long promptId, HttpServletRequest request) {
        Long userId = (Long) request.getAttribute("userId");
        if (userId == null) {
            return Result.error("用户未登录");
        }

        try {
            promptService.incrementUsageCount(promptId, userId);
            return Result.success();
        } catch (Exception e) {
            return Result.error("更新使用次数失败: " + e.getMessage());
        }
    }
}
