# API配置管理功能说明

## 功能概述

新增了完整的API配置管理功能，支持用户保存多个API配置并可以一键切换使用。配置数据保存在云端，支持多设备同步。

## 主要功能

### 1. 配置管理
- ✅ 创建新的API配置
- ✅ 编辑现有配置
- ✅ 删除配置（不能删除当前激活的配置）
- ✅ 一键切换配置
- ✅ 配置列表展示

### 2. 配置测试
- ✅ 单个配置连接测试
- ✅ 实时状态显示（已连接/未连接/连接错误）
- ✅ 测试结果反馈

### 3. 云端同步
- ✅ 配置保存在后端数据库
- ✅ 多设备数据同步
- ✅ 用户隔离（每个用户只能看到自己的配置）

## 技术实现

### 后端实现
1. **数据库表**: `api_configs` - 存储用户的API配置信息
2. **实体类**: `ApiConfig.java` - 配置实体
3. **控制器**: `ApiConfigController.java` - 提供RESTful API
4. **服务层**: `ApiConfigService.java` 和 `ApiConfigServiceImpl.java` - 业务逻辑
5. **数据访问**: `ApiConfigMapper.java` - MyBatis Plus数据访问

### 前端实现
1. **服务层**: `apiConfigService.js` - 封装后端API调用
2. **组件**: `ApiConfig.vue` - 配置管理界面
3. **页面**: `Settings.vue` - 设置页面集成

## API接口

### 基础路径: `/api/api-configs`

#### 1. 获取配置列表
```http
GET /api/api-configs/list
Authorization: Bearer {token}
```

#### 2. 创建配置
```http
POST /api/api-configs
Authorization: Bearer {token}
Content-Type: application/json

{
  "name": "配置名称",
  "type": "custom",
  "apiKey": "API密钥",
  "baseUrl": "https://api.openai.com/v1",
  "selectedModel": "gpt-3.5-turbo",
  "maxTokens": 2000000,
  "unlimitedTokens": 0,
  "temperature": 0.7
}
```

#### 3. 更新配置
```http
PUT /api/api-configs/{configId}
Authorization: Bearer {token}
Content-Type: application/json
```

#### 4. 删除配置
```http
DELETE /api/api-configs/{configId}
Authorization: Bearer {token}
```

#### 5. 激活配置
```http
POST /api/api-configs/{configId}/activate
Authorization: Bearer {token}
```

#### 6. 测试配置
```http
POST /api/api-configs/{configId}/test
Authorization: Bearer {token}
```

#### 7. 获取当前激活配置
```http
GET /api/api-configs/active
Authorization: Bearer {token}
```

## 使用说明

### 1. 创建配置
1. 进入设置页面
2. 点击"新建配置"按钮
3. 填写配置信息：
   - 配置名称：便于识别的名称
   - API密钥：您的API密钥
   - API地址：API服务地址
   - 模型选择：选择或自定义模型
   - 其他参数：Token限制、创造性等
4. 点击"测试连接"验证配置
5. 点击"保存配置"完成创建

### 2. 切换配置
1. 在配置列表中找到要使用的配置
2. 点击"切换使用"按钮
3. 系统会自动切换到新配置

### 3. 编辑配置
1. 在配置列表中点击"编辑"按钮
2. 修改配置信息
3. 点击"更新配置"保存修改

### 4. 删除配置
1. 在配置列表中点击"删除"按钮
2. 确认删除操作
3. 注意：无法删除当前激活的配置

## 数据库表结构

```sql
CREATE TABLE `api_configs` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `name` varchar(100) NOT NULL COMMENT '配置名称',
  `type` varchar(20) NOT NULL COMMENT '类型：official-官方，custom-自定义',
  `api_key` varchar(255) DEFAULT NULL COMMENT 'API密钥',
  `base_url` varchar(255) NOT NULL COMMENT '基础URL',
  `selected_model` varchar(100) DEFAULT NULL COMMENT '选择的模型',
  `max_tokens` int DEFAULT '2000000' COMMENT '最大Token数',
  `unlimited_tokens` tinyint DEFAULT '0' COMMENT '无限Token：0-否，1-是',
  `temperature` decimal(3,2) DEFAULT '0.70' COMMENT '温度参数',
  `top_p` decimal(3,2) DEFAULT '1.00' COMMENT 'Top P参数',
  `frequency_penalty` decimal(3,2) DEFAULT '0.00' COMMENT '频率惩罚',
  `presence_penalty` decimal(3,2) DEFAULT '0.00' COMMENT '存在惩罚',
  `timeout` int DEFAULT '30' COMMENT '超时时间（秒）',
  `stream_mode` tinyint DEFAULT '1' COMMENT '流模式：0-否，1-是',
  `retry_count` int DEFAULT '3' COMMENT '重试次数',
  `custom_headers` text COMMENT '自定义请求头',
  `status` varchar(20) DEFAULT 'disconnected' COMMENT '状态：connected-已连接，disconnected-未连接',
  `is_active` tinyint DEFAULT '0' COMMENT '是否激活：0-否，1-是',
  `created_at` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '逻辑删除：0-未删除，1-已删除',
  PRIMARY KEY (`id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_type` (`type`),
  CONSTRAINT `fk_api_configs_user_id` FOREIGN KEY (`user_id`) REFERENCES `users` (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='API配置表';
```

## 注意事项

1. **安全性**: API密钥等敏感信息存储在后端数据库中，前端不会缓存
2. **用户隔离**: 每个用户只能访问自己的配置，确保数据安全
3. **配置验证**: 创建和更新配置时会进行基本验证
4. **连接测试**: 支持实时测试API连接状态
5. **激活限制**: 同一时间只能有一个配置处于激活状态

## 后续优化

1. 支持配置导入导出
2. 支持配置模板
3. 支持批量操作
4. 增加配置使用统计
5. 支持配置分组管理
