import { authUtils } from '@/services/authApi'
import { ElMessage } from 'element-plus'

/**
 * 路由守卫
 */
export function setupRouterGuards(router) {
  
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    
    // 不需要认证的页面
    const publicPages = ['/login', '/register']
    const isPublicPage = publicPages.includes(to.path)
    
    // 检查是否已登录
    const isLoggedIn = authUtils.isLoggedIn()
    
    if (isPublicPage) {
      // 如果是公开页面
      if (isLoggedIn && to.path === '/login') {
        // 已登录用户访问登录页，重定向到首页
        next('/')
      } else {
        next()
      }
    } else {
      // 需要认证的页面
      if (isLoggedIn) {
        next()
      } else {
        ElMessage.warning('请先登录')
        next('/login')
      }
    }
  })
  
  // 全局后置钩子
  router.afterEach((to, from) => {
    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 写作应用`
    } else {
      document.title = '写作应用'
    }
  })
}
