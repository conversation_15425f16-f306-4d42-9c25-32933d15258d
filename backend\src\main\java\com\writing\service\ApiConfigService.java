package com.writing.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.writing.entity.ApiConfig;

import java.util.List;

/**
 * API配置服务接口
 */
public interface ApiConfigService extends IService<ApiConfig> {
    
    /**
     * 分页获取用户API配置
     */
    IPage<ApiConfig> getUserApiConfigs(Long userId, int current, int size);
    
    /**
     * 获取用户所有API配置列表
     */
    List<ApiConfig> getUserApiConfigList(Long userId);
    
    /**
     * 获取用户指定API配置
     */
    ApiConfig getUserApiConfig(Long userId, Long configId);
    
    /**
     * 创建API配置
     */
    ApiConfig createApiConfig(ApiConfig apiConfig);
    
    /**
     * 更新API配置
     */
    ApiConfig updateApiConfig(ApiConfig apiConfig);
    
    /**
     * 删除API配置
     */
    void deleteApiConfig(Long userId, Long configId);
    
    /**
     * 激活API配置
     */
    void activateApiConfig(Long userId, Long configId);
    
    /**
     * 测试API配置连接
     */
    String testApiConfig(Long userId, Long configId);
    
    /**
     * 获取当前激活的API配置
     */
    ApiConfig getActiveApiConfig(Long userId);
    
    /**
     * 批量保存API配置
     */
    List<ApiConfig> batchSaveApiConfigs(List<ApiConfig> apiConfigs);
}
