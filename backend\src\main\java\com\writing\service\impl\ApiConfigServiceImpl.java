package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.ApiConfig;
import com.writing.mapper.ApiConfigMapper;
import com.writing.service.ApiConfigService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.web.client.RestTemplate;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * API配置服务实现类
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class ApiConfigServiceImpl extends ServiceImpl<ApiConfigMapper, ApiConfig> implements ApiConfigService {

    private final RestTemplate restTemplate;
    
    @Override
    public IPage<ApiConfig> getUserApiConfigs(Long userId, int current, int size) {
        Page<ApiConfig> page = new Page<>(current, size);
        LambdaQueryWrapper<ApiConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiConfig::getUserId, userId)
               .orderByDesc(ApiConfig::getUpdatedAt);
        
        return this.page(page, wrapper);
    }
    
    @Override
    public List<ApiConfig> getUserApiConfigList(Long userId) {
        LambdaQueryWrapper<ApiConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiConfig::getUserId, userId)
               .orderByDesc(ApiConfig::getUpdatedAt);
        
        return this.list(wrapper);
    }
    
    @Override
    public ApiConfig getUserApiConfig(Long userId, Long configId) {
        LambdaQueryWrapper<ApiConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiConfig::getUserId, userId)
               .eq(ApiConfig::getId, configId);
        
        ApiConfig config = this.getOne(wrapper);
        if (config == null) {
            throw new RuntimeException("API配置不存在");
        }
        
        return config;
    }
    
    @Override
    @Transactional
    public ApiConfig createApiConfig(ApiConfig apiConfig) {
        // 设置默认值
        if (apiConfig.getTemperature() == null) {
            apiConfig.setTemperature(new BigDecimal("0.7"));
        }
        if (apiConfig.getMaxTokens() == null) {
            apiConfig.setMaxTokens(2000000);
        }
        if (apiConfig.getUnlimitedTokens() == null) {
            apiConfig.setUnlimitedTokens(0);
        }
        if (apiConfig.getTopP() == null) {
            apiConfig.setTopP(new BigDecimal("1.0"));
        }
        if (apiConfig.getFrequencyPenalty() == null) {
            apiConfig.setFrequencyPenalty(new BigDecimal("0.0"));
        }
        if (apiConfig.getPresencePenalty() == null) {
            apiConfig.setPresencePenalty(new BigDecimal("0.0"));
        }
        if (apiConfig.getTimeout() == null) {
            apiConfig.setTimeout(30);
        }
        if (apiConfig.getStreamMode() == null) {
            apiConfig.setStreamMode(1);
        }
        if (apiConfig.getRetryCount() == null) {
            apiConfig.setRetryCount(3);
        }
        if (apiConfig.getStatus() == null) {
            apiConfig.setStatus("disconnected");
        }
        if (apiConfig.getIsActive() == null) {
            apiConfig.setIsActive(0);
        }
        
        this.save(apiConfig);
        return apiConfig;
    }
    
    @Override
    @Transactional
    public ApiConfig updateApiConfig(ApiConfig apiConfig) {
        // 验证配置是否存在且属于当前用户
        ApiConfig existing = getUserApiConfig(apiConfig.getUserId(), apiConfig.getId());
        
        // 更新配置
        this.updateById(apiConfig);
        return apiConfig;
    }
    
    @Override
    @Transactional
    public void deleteApiConfig(Long userId, Long configId) {
        // 验证配置是否存在且属于当前用户
        getUserApiConfig(userId, configId);
        
        // 如果是激活的配置，需要先取消激活
        LambdaQueryWrapper<ApiConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiConfig::getUserId, userId)
               .eq(ApiConfig::getId, configId)
               .eq(ApiConfig::getIsActive, 1);
        
        ApiConfig activeConfig = this.getOne(wrapper);
        if (activeConfig != null) {
            throw new RuntimeException("无法删除激活中的配置，请先切换到其他配置");
        }
        
        // 删除配置
        this.removeById(configId);
    }
    
    @Override
    @Transactional
    public void activateApiConfig(Long userId, Long configId) {
        // 验证配置是否存在且属于当前用户
        ApiConfig config = getUserApiConfig(userId, configId);
        
        // 取消其他配置的激活状态
        LambdaUpdateWrapper<ApiConfig> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(ApiConfig::getUserId, userId)
                    .set(ApiConfig::getIsActive, 0);
        this.update(updateWrapper);
        
        // 激活指定配置
        config.setIsActive(1);
        this.updateById(config);
        
        log.info("用户 {} 激活了API配置 {}", userId, configId);
    }
    
    @Override
    public String testApiConfig(Long userId, Long configId) {
        ApiConfig config = getUserApiConfig(userId, configId);
        
        try {
            // 构建测试请求
            String url = config.getBaseUrl();
            if (!url.startsWith("http")) {
                url = "https://" + url;
            }
            if (!url.endsWith("/chat/completions")) {
                if (!url.endsWith("/")) {
                    url += "/";
                }
                url += "chat/completions";
            }
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            headers.set("Authorization", "Bearer " + config.getApiKey());
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("model", config.getSelectedModel());
            requestBody.put("messages", List.of(
                Map.of("role", "user", "content", "Hello")
            ));
            requestBody.put("max_tokens", 10);
            requestBody.put("temperature", config.getTemperature());
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            
            // 发送测试请求
            ResponseEntity<String> response = restTemplate.exchange(
                url, HttpMethod.POST, entity, String.class
            );
            
            // 更新配置状态
            config.setStatus("connected");
            this.updateById(config);
            
            log.info("API配置 {} 测试成功", configId);
            return "连接测试成功";
            
        } catch (Exception e) {
            // 更新配置状态
            config.setStatus("error");
            this.updateById(config);
            
            log.error("API配置 {} 测试失败: {}", configId, e.getMessage());
            throw new RuntimeException("连接测试失败: " + e.getMessage());
        }
    }
    
    @Override
    public ApiConfig getActiveApiConfig(Long userId) {
        LambdaQueryWrapper<ApiConfig> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(ApiConfig::getUserId, userId)
               .eq(ApiConfig::getIsActive, 1);
        
        return this.getOne(wrapper);
    }
    
    @Override
    @Transactional
    public List<ApiConfig> batchSaveApiConfigs(List<ApiConfig> apiConfigs) {
        for (ApiConfig config : apiConfigs) {
            if (config.getId() != null) {
                // 更新现有配置
                this.updateById(config);
            } else {
                // 创建新配置
                this.createApiConfig(config);
            }
        }
        return apiConfigs;
    }
}
