package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.Chapter;
import com.writing.entity.Novel;
import com.writing.mapper.NovelMapper;
import com.writing.service.ChapterService;
import com.writing.service.FileUploadService;
import com.writing.service.NovelService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 小说服务实现类
 */
@Service
@RequiredArgsConstructor
public class NovelServiceImpl extends ServiceImpl<NovelMapper, Novel> implements NovelService {

    private final ChapterService chapterService;
    private final FileUploadService fileUploadService;
    
    @Override
    public IPage<Novel> getUserNovels(Long userId, int current, int size) {
        Page<Novel> page = new Page<>(current, size);
        return this.page(page, new LambdaQueryWrapper<Novel>()
                .eq(Novel::getUserId, userId)
                .orderByDesc(Novel::getUpdatedAt));
    }
    
    @Override
    public List<Novel> getUserNovelList(Long userId) {
        return this.list(new LambdaQueryWrapper<Novel>()
                .eq(Novel::getUserId, userId)
                .orderByDesc(Novel::getUpdatedAt));
    }
    
    @Override
    public Novel createNovel(Long userId, Novel novel) {
        novel.setUserId(userId);
        novel.setWordCount(0);
        novel.setChapterCount(0);
        if (novel.getStatus() == null) {
            novel.setStatus("writing");
        }
        this.save(novel);
        return novel;
    }
    
    @Override
    public Novel updateNovel(Long userId, Novel novel) {
        Novel existingNovel = getNovelDetail(userId, novel.getId());
        if (existingNovel == null) {
            throw new RuntimeException("小说不存在");
        }

        // 如果封面发生变化，删除旧的封面文件
        String oldCover = existingNovel.getCover();
        String newCover = novel.getCover();
        if (oldCover != null && !oldCover.equals(newCover)) {
            try {
                fileUploadService.deleteFile(oldCover);
            } catch (Exception e) {
                // 删除失败不影响更新操作，只记录日志
                System.err.println("删除旧封面文件失败: " + oldCover + ", 错误: " + e.getMessage());
            }
        }

        novel.setUserId(userId);
        this.updateById(novel);
        return novel;
    }
    
    @Override
    public boolean deleteNovel(Long userId, Long novelId) {
        Novel novel = getNovelDetail(userId, novelId);
        if (novel == null) {
            throw new RuntimeException("小说不存在");
        }

        // 删除封面文件
        if (novel.getCover() != null) {
            try {
                fileUploadService.deleteFile(novel.getCover());
            } catch (Exception e) {
                // 删除失败不影响删除操作，只记录日志
                System.err.println("删除封面文件失败: " + novel.getCover() + ", 错误: " + e.getMessage());
            }
        }

        return this.removeById(novelId);
    }
    
    @Override
    public Novel getNovelDetail(Long userId, Long novelId) {
        return this.getOne(new LambdaQueryWrapper<Novel>()
                .eq(Novel::getId, novelId)
                .eq(Novel::getUserId, userId));
    }
    
    @Override
    public void updateWordCount(Long novelId) {
        List<Chapter> chapters = chapterService.getChaptersByNovelId(novelId);
        int totalWordCount = chapters.stream()
                .mapToInt(chapter -> chapter.getWordCount() != null ? chapter.getWordCount() : 0)
                .sum();
        
        Novel novel = new Novel();
        novel.setId(novelId);
        novel.setWordCount(totalWordCount);
        novel.setChapterCount(chapters.size());
        this.updateById(novel);
    }
}
