import apiClient from './apiClient.js'

/**
 * API配置服务
 */
class ApiConfigService {
  
  /**
   * 获取用户所有API配置列表
   */
  async getApiConfigList() {
    try {
      const response = await apiClient.get('/api-configs/list')
      return response
    } catch (error) {
      console.error('获取API配置列表失败:', error)
      throw error
    }
  }
  
  /**
   * 分页获取API配置
   */
  async getApiConfigs(current = 1, size = 10) {
    try {
      const response = await apiClient.get('/api-configs', {
        params: { current, size }
      })
      return response.data
    } catch (error) {
      console.error('分页获取API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 获取API配置详情
   */
  async getApiConfig(configId) {
    try {
      const response = await apiClient.get(`/api-configs/${configId}`)
      return response.data
    } catch (error) {
      console.error('获取API配置详情失败:', error)
      throw error
    }
  }
  
  /**
   * 创建API配置
   */
  async createApiConfig(configData) {
    try {
      const response = await apiClient.post('/api-configs', configData)
      return response.data
    } catch (error) {
      console.error('创建API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 更新API配置
   */
  async updateApiConfig(configId, configData) {
    try {
      const response = await apiClient.put(`/api-configs/${configId}`, configData)
      return response.data
    } catch (error) {
      console.error('更新API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 删除API配置
   */
  async deleteApiConfig(configId) {
    try {
      const response = await apiClient.delete(`/api-configs/${configId}`)
      return response.data
    } catch (error) {
      console.error('删除API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 激活API配置
   */
  async activateApiConfig(configId) {
    try {
      const response = await apiClient.post(`/api-configs/${configId}/activate`)
      return response.data
    } catch (error) {
      console.error('激活API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 测试API配置连接
   */
  async testApiConfig(configId) {
    try {
      const response = await apiClient.post(`/api-configs/${configId}/test`)
      return response.data
    } catch (error) {
      console.error('测试API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 获取当前激活的API配置
   */
  async getActiveApiConfig() {
    try {
      const response = await apiClient.get('/api-configs/active')
      return response
    } catch (error) {
      console.error('获取激活API配置失败:', error)
      throw error
    }
  }
  
  /**
   * 批量保存API配置
   */
  async batchSaveApiConfigs(configs) {
    try {
      const response = await apiClient.post('/api-configs/batch', configs)
      return response.data
    } catch (error) {
      console.error('批量保存API配置失败:', error)
      throw error
    }
  }
}

export default new ApiConfigService()
