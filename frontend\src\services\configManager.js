import apiConfigService from './apiConfigService.js'
import { ElMessage } from 'element-plus'

/**
 * 配置管理器 - 统一管理API配置
 */
class ConfigManager {
  constructor() {
    this.currentConfig = null
    this.isLoading = false
    this.listeners = []
  }

  /**
   * 获取当前激活的配置
   */
  async getCurrentConfig() {
    
    if (this.currentConfig) {
      return this.currentConfig
    }

    return await this.loadActiveConfig()
  }

  /**
   * 从后端加载激活的配置
   */
  async loadActiveConfig() {
    if (this.isLoading) {
      return this.currentConfig
    }

    this.isLoading = true
    try {
      // 检查用户是否已登录
      const token = localStorage.getItem('token')
      if (!token) {
        console.log('用户未登录，使用本地配置')
        return this.getLocalConfig()
      }

      const result = await apiConfigService.getActiveApiConfig()
      if (result && result.data) {
        this.currentConfig = this.formatConfig(result.data)
        this.notifyListeners()
        return this.currentConfig
      } else {
        console.log('没有激活的配置，使用本地配置')
        return this.getLocalConfig()
      }
    } catch (error) {
      console.error('加载配置失败:', error)
      // 如果是401错误，使用本地配置
      if (error.response && error.response.status === 401) {
        console.log('用户未授权，使用本地配置')
        return this.getLocalConfig()
      }
      // 其他错误也使用本地配置作为降级
      return this.getLocalConfig()
    } finally {
      this.isLoading = false
    }
  }

  /**
   * 获取本地配置（降级方案）
   */
  getLocalConfig() {
    try {
      const saved = localStorage.getItem('customApiConfig')
      if (saved) {
        const config = JSON.parse(saved)
        return {
          apiKey: config.apiKey || '',
          baseURL: config.baseURL || config.baseUrl || 'https://api.openai.com/v1',
          selectedModel: config.selectedModel || 'gpt-3.5-turbo',
          maxTokens: config.unlimitedTokens ? null : (config.maxTokens || 2000000),
          temperature: config.temperature || 0.7,
          defaultModel: config.selectedModel || 'gpt-3.5-turbo'
        }
      }
    } catch (error) {
      console.error('加载本地配置失败:', error)
    }

    // 返回默认配置
    return {
      apiKey: '',
      baseURL: 'https://api.openai.com/v1',
      selectedModel: 'gpt-3.5-turbo',
      maxTokens: 2000000,
      temperature: 0.7,
      defaultModel: 'gpt-3.5-turbo'
    }
  }

  /**
   * 格式化后端配置为前端使用的格式
   */
  formatConfig(backendConfig) {
    return {
      apiKey: backendConfig.apiKey || '',
      baseURL: backendConfig.baseUrl || 'https://api.openai.com/v1',
      selectedModel: backendConfig.selectedModel || 'gpt-3.5-turbo',
      maxTokens: backendConfig.unlimitedTokens === 1 ? null : (backendConfig.maxTokens || 2000000),
      temperature: parseFloat(backendConfig.temperature) || 0.7,
      defaultModel: backendConfig.selectedModel || 'gpt-3.5-turbo',
      // 保留后端配置的其他字段
      id: backendConfig.id,
      name: backendConfig.name,
      type: backendConfig.type,
      status: backendConfig.status
    }
  }

  /**
   * 刷新配置
   */
  async refreshConfig() {
    this.currentConfig = null
    return await this.loadActiveConfig()
  }

  /**
   * 检查配置是否有效
   */
  isConfigValid(config = null) {
    const cfg = config || this.currentConfig
    return cfg && cfg.apiKey && cfg.baseURL
  }

  /**
   * 添加配置变更监听器
   */
  addListener(callback) {
    this.listeners.push(callback)
  }

  /**
   * 移除配置变更监听器
   */
  removeListener(callback) {
    const index = this.listeners.indexOf(callback)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }

  /**
   * 通知所有监听器配置已变更
   */
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.currentConfig)
      } catch (error) {
        console.error('配置监听器执行失败:', error)
      }
    })
  }

  /**
   * 清除当前配置缓存
   */
  clearCache() {
    this.currentConfig = null
  }
}

export default new ConfigManager()
