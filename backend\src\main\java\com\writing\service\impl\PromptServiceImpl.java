package com.writing.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.writing.entity.Prompt;
import com.writing.mapper.PromptMapper;
import com.writing.service.PromptService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 提示词服务实现类
 */
@Service
@RequiredArgsConstructor
public class PromptServiceImpl extends ServiceImpl<PromptMapper, Prompt> implements PromptService {
    
    @Override
    public List<Prompt> getPromptsByUserId(Long userId, String category) {
        LambdaQueryWrapper<Prompt> queryWrapper = new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getUserId, userId)
                .orderByDesc(Prompt::getUsageCount)
                .orderByDesc(Prompt::getUpdatedAt);
        
        // 如果指定了分类，添加分类筛选
        if (StringUtils.hasText(category)) {
            queryWrapper.eq(Prompt::getCategory, category);
        }
        
        return this.list(queryWrapper);
    }
    
    @Override
    public Prompt getPromptById(Long promptId, Long userId) {
        return this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
    }
    
    @Override
    public Prompt createPrompt(Prompt prompt) {
        // 设置默认值
        if (prompt.getIsDefault() == null) {
            prompt.setIsDefault(0); // 0表示非默认，1表示默认
        }
        if (prompt.getUsageCount() == null) {
            prompt.setUsageCount(0);
        }
        
        this.save(prompt);
        return prompt;
    }
    
    @Override
    public Prompt updatePrompt(Prompt prompt, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt existingPrompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, prompt.getId())
                .eq(Prompt::getUserId, userId));
        
        if (existingPrompt == null) {
            throw new RuntimeException("提示词不存在或无权限修改");
        }
        
        // 保留原有的使用次数和默认状态
        prompt.setUsageCount(existingPrompt.getUsageCount());
        if (prompt.getIsDefault() == null) {
            prompt.setIsDefault(existingPrompt.getIsDefault());
        }
        
        this.updateById(prompt);
        return prompt;
    }
    
    @Override
    public boolean deletePrompt(Long promptId, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt prompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
        
        if (prompt == null) {
            throw new RuntimeException("提示词不存在或无权限删除");
        }
        
        // 不允许删除默认提示词
        if (prompt.getIsDefault() != null && prompt.getIsDefault() == 1) {
            throw new RuntimeException("不能删除默认提示词");
        }
        
        return this.removeById(promptId);
    }
    
    @Override
    public void incrementUsageCount(Long promptId, Long userId) {
        // 验证提示词是否存在且属于当前用户
        Prompt prompt = this.getOne(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getId, promptId)
                .eq(Prompt::getUserId, userId));
        
        if (prompt == null) {
            throw new RuntimeException("提示词不存在或无权限访问");
        }
        
        // 增加使用次数
        prompt.setUsageCount(prompt.getUsageCount() + 1);
        this.updateById(prompt);
    }
    
    @Override
    public List<Prompt> getDefaultPrompts() {
        return this.list(new LambdaQueryWrapper<Prompt>()
                .eq(Prompt::getIsDefault, 1)
                .orderByAsc(Prompt::getCategory)
                .orderByAsc(Prompt::getId));
    }
}
